/**
 * 手机号绑定页面
 * 用于微信授权后绑定手机号的流程
 */
import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useAuth, AuthStatus } from '../hooks/useAuth'
import { SMSType } from '../services/authService'
import { validatePhoneNumber } from '../utils/encryption'
import { isFromWeiXin } from '../utils/networkUtils'
import Toast from '../components/Toast'
import { useToast } from '../hooks/useToast'

const PhoneBinding: React.FC = () => {
  const navigate = useNavigate()
  const { toast, showError, showSuccess, hideToast } = useToast()
  const { authStatus, isLoading, error, bindPhone, signup, login, sendSMSCode, clearError } =
    useAuth()

  const [formData, setFormData] = useState({
    mobile: '',
    smsCode: '',
  })
  const [countdown, setCountdown] = useState(0)
  const [isAgreed, setIsAgreed] = useState(true)

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => clearTimeout(timer)
  }, [countdown])

  // 监听认证状态变化
  useEffect(() => {
    if (authStatus === AuthStatus.AUTHENTICATED) {
      console.log('✅ 登录/绑定成功，跳转到主页')
      navigate('/', { replace: true })
    }
  }, [authStatus, navigate])

  // 输入框变化处理
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (error) clearError()
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (!formData.mobile) {
      showError('请输入手机号')
      return
    }

    if (!validatePhoneNumber(formData.mobile)) {
      showError('手机号格式不正确')
      return
    }

    // 根据环境选择短信类型
    const smsType = isFromWeiXin() ? SMSType.BIND : SMSType.LOGIN
    const success = await sendSMSCode(formData.mobile, smsType)
    if (success) {
      setCountdown(60)
      showSuccess('验证码已发送')
    }
  }

  // 提交处理（绑定手机号或登录）
  const handleSubmit = async () => {
    if (!formData.mobile) {
      showError('请输入手机号')
      return
    }

    if (!validatePhoneNumber(formData.mobile)) {
      showError('手机号格式不正确')
      return
    }

    if (!formData.smsCode) {
      showError('请输入验证码')
      return
    }

    if (!isAgreed) {
      showError('请同意用户协议')
      return
    }

    let success = false

    try {
      if (isFromWeiXin()) {
        // 微信环境：尝试绑定，如果失败则注册
        success = await bindPhone(formData.mobile, formData.smsCode)
        if (!success) {
          success = await signup(formData.mobile, formData.smsCode)
        }
      } else {
        // 非微信环境：直接登录
        success = await login(formData.mobile, formData.smsCode)
      }

      if (success) {
        console.log('✅ 操作成功')
      }
    } catch (error) {
      console.error('操作失败:', error)
    }
  }

  // 获取页面信息
  const getPageInfo = () => {
    if (isFromWeiXin()) {
      return {
        title: '绑定手机号',
        subtitle: '为了更好地为您服务，请绑定您的手机号',
        buttonText: '绑定手机号',
      }
    } else {
      return {
        title: '手机号登录',
        subtitle: '欢迎回来，请使用手机号登录',
        buttonText: '登录',
      }
    }
  }

  const pageInfo = getPageInfo()

  // 渲染页面
  return (
    <>
      <Toast
        message={toast.message}
        type={toast.type}
        visible={toast.visible}
        onClose={hideToast}
      />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="max-w-md w-full"
        >
          {/* 应用图标和标题 */}
          <motion.div
            className="text-center mb-8"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.1, duration: 0.4 }}
          >
            <div className="mx-auto w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <svg
                className="w-10 h-10 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              健康助手
            </h1>
            <p className="text-gray-600">{pageInfo.subtitle}</p>
          </motion.div>

          {/* 表单 */}
          <motion.div
            className="bg-white rounded-2xl shadow-lg p-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            {/* 错误提示 */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}

            {/* 手机号输入 */}
            <motion.div
              className="mb-4"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">手机号</label>
              <input
                type="tel"
                value={formData.mobile}
                onChange={e => handleInputChange('mobile', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-blue-400"
                placeholder="请输入手机号"
                maxLength={11}
              />
            </motion.div>

            {/* 验证码输入 */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.3 }}
            >
              <label className="block text-sm font-medium text-gray-700 mb-2">验证码</label>
              <div className="flex gap-3">
                <input
                  type="text"
                  value={formData.smsCode}
                  onChange={e => handleInputChange('smsCode', e.target.value)}
                  className="flex-1 px-2 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-blue-400"
                  placeholder="请输入验证码"
                  maxLength={6}
                />
                <motion.button
                  onClick={handleSendCode}
                  disabled={countdown > 0 || !formData.mobile || isLoading}
                  className={`px-4 py-3 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                    countdown > 0 || !formData.mobile || isLoading
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md'
                  }`}
                  whileHover={
                    countdown === 0 && formData.mobile && !isLoading ? { scale: 1.05 } : {}
                  }
                  whileTap={countdown === 0 && formData.mobile && !isLoading ? { scale: 0.95 } : {}}
                >
                  {countdown > 0 ? `${countdown}s` : '获取验证码'}
                </motion.button>
              </div>
            </motion.div>

            {/* 协议同意 */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.3 }}
            >
              <label className="flex items-start">
                <input
                  type="checkbox"
                  checked={isAgreed}
                  onChange={e => setIsAgreed(e.target.checked)}
                  className="mt-1 mr-2 accent-blue-600"
                />
                <span className="text-sm text-gray-600">
                  我已阅读并同意
                  <a
                    href="/common-doc/?docpath=agreement&readonly=true"
                    className="text-blue-600 hover:underline mx-1 transition-colors duration-200 hover:text-blue-800"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    《用户协议》
                  </a>
                </span>
              </label>
            </motion.div>

            {/* 提交按钮 */}
            <motion.button
              onClick={handleSubmit}
              disabled={isLoading || !formData.mobile || !formData.smsCode || !isAgreed}
              className={`w-full py-3 rounded-lg font-medium transition-all duration-200 ${
                isLoading || !formData.mobile || !formData.smsCode || !isAgreed
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 hover:shadow-lg'
              }`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.3 }}
              whileHover={
                !isLoading && formData.mobile && formData.smsCode && isAgreed ? { scale: 1.02 } : {}
              }
              whileTap={
                !isLoading && formData.mobile && formData.smsCode && isAgreed ? { scale: 0.98 } : {}
              }
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  处理中...
                </div>
              ) : (
                pageInfo.buttonText
              )}
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </>
  )
}

export default PhoneBinding
