/**
 * 受保护的路由组件
 * 用于需要认证的页面，现在由AuthProvider统一处理认证逻辑
 */
import React from 'react'
import { useAuthContext } from './AuthProvider'
import { AuthStatus } from '../hooks/useAuth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean // 是否需要认证，默认true
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requireAuth = true }) => {
  const { authStatus, isLoading } = useAuthContext()

  // 如果不需要认证，直接显示内容
  if (!requireAuth) {
    return <>{children}</>
  }

  // 加载中显示
  if (isLoading || authStatus === AuthStatus.LOADING) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证身份...</p>
        </div>
      </div>
    )
  }

  // 未认证或需要绑定手机号的情况由AuthProvider统一处理
  // 这里只有认证成功的情况才会渲染内容
  if (authStatus === AuthStatus.AUTHENTICATED) {
    return <>{children}</>
  }

  // 其他状态（未认证、需要绑定手机号）显示加载状态
  // AuthProvider会处理跳转逻辑
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">正在处理认证...</p>
      </div>
    </div>
  )
}

export default ProtectedRoute
