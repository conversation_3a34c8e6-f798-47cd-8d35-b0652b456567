/**
 * 认证提供者组件
 * 全局管理认证状态和微信授权跳转逻辑
 */
import React, { createContext, useContext, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAuth, AuthStatus } from '../hooks/useAuth'
import { isFromWeiXin, getQueryVariableValue } from '../utils/networkUtils'

interface AuthContextType {
  authStatus: AuthStatus
  user: any
  isLoading: boolean
  error: string | null
  login: (mobile: string, smsCode: string) => Promise<boolean>
  logout: () => Promise<void>
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuthContext = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { authStatus, user, isLoading, error, login, logout, clearError } = useAuth()

  // 获取微信公众号授权URL
  const getWeChatAuthUrl = () => {
    const appId = window.AppConfig?.WECHAT_CONFIG?.APP_ID || 'your_wechat_appid'
    const redirectUri = encodeURIComponent(window.location.origin + window.location.pathname)
    const state = 'health_assistant_' + Date.now()

    return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
  }

  // 处理认证状态变化
  useEffect(() => {
    const currentPath = location.pathname
    const isPhoneBindingPage = currentPath === '/phone-binding'
    const hasWeChatCode = getQueryVariableValue('code')

    console.log('🔍 AuthProvider - 认证状态检查:', {
      authStatus,
      currentPath,
      isPhoneBindingPage,
      hasWeChatCode,
      isFromWeiXin: isFromWeiXin(),
    })

    // 如果是加载状态，不做任何操作
    if (authStatus === AuthStatus.LOADING) {
      console.log('⏳ AuthProvider - 加载中，暂不处理')
      return
    }

    switch (authStatus) {
      case AuthStatus.UNAUTHENTICATED:
        // 未认证状态
        if (!isPhoneBindingPage) {
          if (isFromWeiXin()) {
            if (!hasWeChatCode) {
              // 在微信中但没有授权码，跳转到微信授权
              console.log('🔄 AuthProvider - 跳转到微信授权页面')
              window.location.href = getWeChatAuthUrl()
              return
            }
            // 有微信授权码但未认证，等待useAuth处理
            console.log('⏳ AuthProvider - 有微信授权码，等待认证处理')
          } else {
            // 非微信环境，跳转到手机号绑定页面
            console.log('🔄 AuthProvider - 非微信环境，跳转到手机号绑定页面')
            navigate('/phone-binding', {
              state: { from: currentPath },
              replace: true,
            })
          }
        }
        break

      case AuthStatus.NEED_PHONE_BINDING:
        // 需要绑定手机号
        if (!isPhoneBindingPage) {
          console.log('🔄 AuthProvider - 需要绑定手机号，跳转到绑定页面')
          navigate('/phone-binding', {
            state: { from: currentPath },
            replace: true,
          })
        } else {
          console.log('✅ AuthProvider - 已在绑定页面，等待用户操作')
        }
        break

      case AuthStatus.AUTHENTICATED:
        // 已认证状态
        console.log('✅ AuthProvider - 用户已认证')
        if (isPhoneBindingPage) {
          console.log('🔄 AuthProvider - 用户已认证，从绑定页面跳转到主页')
          navigate('/', { replace: true })
        }
        break
    }
  }, [authStatus, location.pathname, navigate])

  // 监听微信授权回调
  useEffect(() => {
    const code = getQueryVariableValue('code')
    const state = getQueryVariableValue('state')

    if (code && state && state.startsWith('health_assistant_')) {
      console.log('🔄 检测到微信授权回调:', { code, state })
      // 微信授权码会由useAuth自动处理
      // 这里可以清理URL参数
      const url = new URL(window.location.href)
      url.searchParams.delete('code')
      url.searchParams.delete('state')
      window.history.replaceState({}, '', url.toString())
    }
  }, [])

  const contextValue: AuthContextType = {
    authStatus,
    user,
    isLoading,
    error,
    login,
    logout,
    clearError,
  }

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}
