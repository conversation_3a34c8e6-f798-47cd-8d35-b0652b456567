/**
 * 认证状态管理Hook
 * 管理用户登录状态和认证流程
 */
import { useCallback, useEffect, useState } from 'react'
import {
  LoginResponse,
  SMSType,
  UserInfo,
  logout as apiLogout,
  bindPhoneWechat,
  getSMSCode,
  loginBySMSCode,
  performAuthFlow,
  signupBySMSCode,
} from '../services/authService'
import { aesEncrypt, validatePhoneNumber } from '../utils/encryption'
import {
  clearLocalToken,
  getUserInfoFromToken,
  isFromWeiXin,
  setToken,
  shouldRefreshToken,
  validateToken,
} from '../utils/networkUtils'

// 认证状态枚举
export enum AuthStatus {
  LOADING = 'loading', // 检查中
  AUTHENTICATED = 'authenticated', // 已认证
  UNAUTHENTICATED = 'unauthenticated', // 未认证
  NEED_PHONE_BINDING = 'need_phone_binding', // 需要绑定手机号
}

// Hook返回类型
export interface UseAuthReturn {
  // 状态
  authStatus: AuthStatus
  user: UserInfo | null
  isLoading: boolean
  error: string | null
  wxCode: string | null

  // 方法
  login: (mobile: string, smsCode: string) => Promise<boolean>
  signup: (mobile: string, smsCode: string) => Promise<boolean>
  bindPhone: (mobile: string, smsCode: string) => Promise<boolean>
  sendSMSCode: (mobile: string, type?: SMSType) => Promise<boolean>
  logout: () => Promise<void>
  clearError: () => void
  checkAuthStatus: () => Promise<void>
}

// 🔥 修复：全局状态管理，确保所有组件实例共享同一个状态
let globalAuthStatus = AuthStatus.LOADING
let globalUser: UserInfo | null = null
let globalIsLoading = false
let globalError: string | null = null
let globalWxCode: string | null = null

// 状态更新监听器
const authStatusListeners = new Set<(status: AuthStatus) => void>()
const userListeners = new Set<(user: UserInfo | null) => void>()
const loadingListeners = new Set<(loading: boolean) => void>()
const errorListeners = new Set<(error: string | null) => void>()
const wxCodeListeners = new Set<(code: string | null) => void>()

// 全局状态更新函数
const updateGlobalAuthStatus = (status: AuthStatus) => {
  if (globalAuthStatus !== status) {
    globalAuthStatus = status
    authStatusListeners.forEach(listener => listener(status))
  }
}

const updateGlobalUser = (user: UserInfo | null) => {
  if (globalUser !== user) {
    globalUser = user
    userListeners.forEach(listener => listener(user))
  }
}

const updateGlobalLoading = (loading: boolean) => {
  if (globalIsLoading !== loading) {
    globalIsLoading = loading
    loadingListeners.forEach(listener => listener(loading))
  }
}

const updateGlobalError = (error: string | null) => {
  if (globalError !== error) {
    globalError = error
    errorListeners.forEach(listener => listener(error))
  }
}

const updateGlobalWxCode = (code: string | null) => {
  if (globalWxCode !== code) {
    globalWxCode = code
    wxCodeListeners.forEach(listener => listener(code))
  }
}

export const useAuth = (): UseAuthReturn => {
  const [authStatus, setAuthStatus] = useState<AuthStatus>(globalAuthStatus)
  const [user, setUser] = useState<UserInfo | null>(globalUser)
  const [isLoading, setIsLoading] = useState(globalIsLoading)
  const [error, setError] = useState<string | null>(globalError)
  const [wxCode, setWxCode] = useState<string | null>(globalWxCode)

  // 🔥 修复：订阅全局状态变化
  useEffect(() => {
    authStatusListeners.add(setAuthStatus)
    userListeners.add(setUser)
    loadingListeners.add(setIsLoading)
    errorListeners.add(setError)
    wxCodeListeners.add(setWxCode)

    return () => {
      authStatusListeners.delete(setAuthStatus)
      userListeners.delete(setUser)
      loadingListeners.delete(setIsLoading)
      errorListeners.delete(setError)
      wxCodeListeners.delete(setWxCode)
    }
  }, [])

  // 清除错误
  const clearError = useCallback(() => {
    updateGlobalError(null)
  }, [])

  // 设置用户信息和认证状态
  const setAuthenticatedUser = useCallback((loginResponse: LoginResponse) => {
    console.log('🔑 Token已存储')
    setToken(loginResponse.idToken)
    updateGlobalUser(loginResponse.user)
    updateGlobalAuthStatus(AuthStatus.AUTHENTICATED)
    updateGlobalError(null)
    console.log('✅ 用户认证状态已更新')
  }, [])

  // 检查认证状态
  const checkAuthStatus = useCallback(async () => {
    console.log('🔍 useAuth - 开始检查认证状态')
    updateGlobalLoading(true)

    try {
      // 🔥 修复：检查本地存储信息
      const hasToken = !!localStorage.getItem('token')
      const hasRefreshToken = !!localStorage.getItem('refreshToken')
      const hasUserInfo = !!localStorage.getItem('userInfo')
      const tokenExpired = !validateToken()
      const shouldRefresh = shouldRefreshToken()

      console.log('📊 本地存储信息:', {
        hasToken,
        hasRefreshToken,
        hasUserInfo,
        tokenExpired,
        shouldRefresh,
        isFromWeiXin: isFromWeiXin()
      })

      // 首先检查本地是否有有效token
      const hasValidToken = validateToken()

      if (hasValidToken) {
        console.log('✅ 本地有有效token')

        // 从token中提取用户信息
        const tokenUserInfo = getUserInfoFromToken()
        if (tokenUserInfo) {
          console.log('👤 从token中获取用户信息:', tokenUserInfo)
          const user = {
            id: tokenUserInfo.userId,
            mobile: tokenUserInfo.mobile,
            nickname: tokenUserInfo.username || tokenUserInfo.nickname,
            avatar: tokenUserInfo.avatar,
            wechatOpenId: tokenUserInfo.wechatOpenId,
          }
          updateGlobalUser(user)
        }

        // 🔥 修复：直接设置为已认证状态，不再循环检查
        updateGlobalAuthStatus(AuthStatus.AUTHENTICATED)
        updateGlobalError(null)
        console.log('✅ 认证状态设置为 AUTHENTICATED')

        // 检查是否需要刷新token
        if (shouldRefreshToken()) {
          console.log('⏰ Token即将过期，需要刷新')
          // TODO: 实现token刷新逻辑
        }
      } else {
        console.log('❌ 本地没有有效token，尝试微信授权流程')

        // 没有有效token，尝试微信授权流程
        const authResult = await performAuthFlow()

        if (authResult.success && authResult.data) {
          console.log('✅ 微信授权成功')
          setAuthenticatedUser(authResult.data)
        } else if (authResult.needPhoneBinding) {
          console.log('📱 需要绑定手机号')
          updateGlobalAuthStatus(AuthStatus.NEED_PHONE_BINDING)
          updateGlobalWxCode(authResult.wxCode || null)
          updateGlobalError(authResult.error || null)
        } else {
          console.log('❌ 微信授权失败，设置为未认证状态')
          updateGlobalAuthStatus(AuthStatus.UNAUTHENTICATED)
          updateGlobalError(authResult.error || null)
        }
      }
    } catch (error: any) {
      console.error('❌ 检查认证状态失败:', error)
      updateGlobalAuthStatus(AuthStatus.UNAUTHENTICATED)
      updateGlobalError(error.message || '认证检查失败')
      clearLocalToken()
    } finally {
      updateGlobalLoading(false)
      console.log('🏁 useAuth - 认证检查完成')
    }
  }, [setAuthenticatedUser]) // 🔥 修复：移除authStatus依赖，避免循环

  // 手机号登录
  const login = useCallback(
    async (mobile: string, smsCode: string): Promise<boolean> => {
      updateGlobalLoading(true)
      updateGlobalError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          updateGlobalError('手机号格式不正确')
          return false
        }

        const encryptedMobile = aesEncrypt(mobile)
        const loginResponse = await loginBySMSCode(encryptedMobile, smsCode)
        setAuthenticatedUser(loginResponse)
        return true
      } catch (error: any) {
        console.error('登录失败:', error)
        updateGlobalError(error.response?.data?.title || '登录失败')
        return false
      } finally {
        updateGlobalLoading(false)
      }
    },
    [setAuthenticatedUser]
  )

  // 手机号注册（有微信code的情况）
  const signup = useCallback(
    async (mobile: string, smsCode: string): Promise<boolean> => {
      // 在非微信环境下，直接使用手机号登录API
      if (!isFromWeiXin() || !wxCode) {
        return login(mobile, smsCode)
      }

      updateGlobalLoading(true)
      updateGlobalError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          updateGlobalError('手机号格式不正确')
          return false
        }

        const loginResponse = await signupBySMSCode(mobile, smsCode, wxCode)
        setAuthenticatedUser(loginResponse)
        return true
      } catch (error: any) {
        console.error('注册失败:', error)
        updateGlobalError(error.response?.data?.title || '注册失败')
        return false
      } finally {
        updateGlobalLoading(false)
      }
    },
    [wxCode, setAuthenticatedUser, login]
  )

  // 绑定手机号
  const bindPhone = useCallback(
    async (mobile: string, smsCode: string): Promise<boolean> => {
      // 在非微信环境下，直接使用手机号登录API
      if (!isFromWeiXin() || !wxCode) {
        return login(mobile, smsCode)
      }

      updateGlobalLoading(true)
      updateGlobalError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          updateGlobalError('手机号格式不正确')
          return false
        }

        const loginResponse = await bindPhoneWechat(mobile, wxCode, smsCode)
        setAuthenticatedUser(loginResponse)
        return true
      } catch (error: any) {
        console.error('绑定手机号失败:', error)
        updateGlobalError(error.response?.data?.title || '绑定失败')
        return false
      } finally {
        updateGlobalLoading(false)
      }
    },
    [wxCode, setAuthenticatedUser, login]
  )

  // 发送短信验证码
  const sendSMSCode = useCallback(
    async (mobile: string, type: SMSType = SMSType.LOGIN): Promise<boolean> => {
      updateGlobalError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          updateGlobalError('手机号格式不正确')
          return false
        }

        await getSMSCode(mobile, type)
        return true
      } catch (error: any) {
        console.error('发送验证码失败:', error)
        updateGlobalError(error.response?.data?.title || '发送验证码失败')
        return false
      }
    },
    []
  )

  // 退出登录
  const logout = useCallback(async (): Promise<void> => {
    try {
      await apiLogout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地状态
      clearLocalToken()
      updateGlobalUser(null)
      updateGlobalAuthStatus(AuthStatus.UNAUTHENTICATED)
      updateGlobalWxCode(null)
      updateGlobalError(null)
    }
  }, [])

  // 🔥 修复：监听认证相关事件，但要考虑当前认证状态
  useEffect(() => {
    const handleTokenExpired = () => {
      console.log('🔄 Token已过期，清除认证状态')
      updateGlobalUser(null)
      updateGlobalAuthStatus(AuthStatus.UNAUTHENTICATED)
      updateGlobalError('登录已过期，请重新登录')
    }

    const handleNeedPhoneBinding = () => {
      // 🔥 修复：如果用户已经认证成功，忽略这个事件
      // 这个事件可能是由于某些API调用返回的错误触发的，但用户实际上已经登录成功
      if (globalAuthStatus === AuthStatus.AUTHENTICATED) {
        console.log('⚠️ 用户已认证，忽略needPhoneBinding事件')
        return
      }

      console.log('🔄 需要绑定手机号')
      updateGlobalAuthStatus(AuthStatus.NEED_PHONE_BINDING)
      updateGlobalError('需要绑定手机号')
    }

    const handleAuthRequired = () => {
      console.log('🔄 需要重新认证')
      updateGlobalUser(null)
      updateGlobalAuthStatus(AuthStatus.UNAUTHENTICATED)
      updateGlobalError('需要重新登录')
    }

    // 监听HTTP拦截器触发的认证事件
    window.addEventListener('auth:tokenExpired', handleTokenExpired)
    window.addEventListener('auth:needPhoneBinding', handleNeedPhoneBinding)
    window.addEventListener('auth:authRequired', handleAuthRequired)

    return () => {
      window.removeEventListener('auth:tokenExpired', handleTokenExpired)
      window.removeEventListener('auth:needPhoneBinding', handleNeedPhoneBinding)
      window.removeEventListener('auth:authRequired', handleAuthRequired)
    }
  }, []) // 🔥 修复：移除authStatus依赖，使用globalAuthStatus

  // 🔥 修复：确保只有一个实例初始化认证状态
  useEffect(() => {
    // 只有当全局状态还是LOADING时才检查认证状态
    if (globalAuthStatus === AuthStatus.LOADING) {
      console.log('🔍 首次初始化认证状态检查')
      checkAuthStatus()
    } else {
      console.log('🔍 认证状态已初始化，跳过检查:', globalAuthStatus)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    authStatus,
    user,
    isLoading,
    error,
    wxCode,
    login,
    signup,
    bindPhone,
    sendSMSCode,
    logout,
    clearError,
    checkAuthStatus,
  }
}
