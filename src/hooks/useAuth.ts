/**
 * 认证状态管理Hook
 * 管理用户登录状态和认证流程
 */
import { useState, useEffect, useCallback } from 'react'
import {
  performAuthFlow,
  LoginResponse,
  UserInfo,
  loginBySMSCode,
  getSMSCode,
  SMSType,
  signupBySMSCode,
  bindPhoneWechat,
  logout as apiLogout,
} from '../services/authService'
import {
  setToken,
  clearLocalToken,
  validateToken,
  shouldRefreshToken,
  getUserInfoFromToken,
  isFromWeiXin,
} from '../utils/networkUtils'
import { aesEncrypt, validatePhoneNumber } from '../utils/encryption'

// 认证状态枚举
export enum AuthStatus {
  LOADING = 'loading', // 检查中
  AUTHENTICATED = 'authenticated', // 已认证
  UNAUTHENTICATED = 'unauthenticated', // 未认证
  NEED_PHONE_BINDING = 'need_phone_binding', // 需要绑定手机号
}

// Hook返回类型
export interface UseAuthReturn {
  // 状态
  authStatus: AuthStatus
  user: UserInfo | null
  isLoading: boolean
  error: string | null
  wxCode: string | null

  // 方法
  login: (mobile: string, smsCode: string) => Promise<boolean>
  signup: (mobile: string, smsCode: string) => Promise<boolean>
  bindPhone: (mobile: string, smsCode: string) => Promise<boolean>
  sendSMSCode: (mobile: string, type?: SMSType) => Promise<boolean>
  logout: () => Promise<void>
  clearError: () => void
  checkAuthStatus: () => Promise<void>
}

export const useAuth = (): UseAuthReturn => {
  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.LOADING)
  const [user, setUser] = useState<UserInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [wxCode, setWxCode] = useState<string | null>(null)

  // 清除错误
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // 设置用户信息和认证状态
  const setAuthenticatedUser = useCallback((loginResponse: LoginResponse) => {
    setToken(loginResponse.idToken)
    setUser(loginResponse.user)
    setAuthStatus(AuthStatus.AUTHENTICATED)
    setError(null)
  }, [])

  // 检查认证状态
  const checkAuthStatus = useCallback(async () => {
    console.log('🔍 开始检查认证状态，当前状态:', authStatus)
    setIsLoading(true)

    try {
      // 首先检查本地是否有有效token
      const hasValidToken = validateToken()
      console.log('🔑 Token验证结果:', hasValidToken)

      if (hasValidToken) {
        console.log('✅ 本地有有效token，设置为已认证状态')

        // 从token中提取用户信息
        const tokenUserInfo = getUserInfoFromToken()
        if (tokenUserInfo) {
          console.log('👤 从token中获取用户信息:', tokenUserInfo)
          const user = {
            id: tokenUserInfo.userId,
            mobile: tokenUserInfo.mobile,
            nickname: tokenUserInfo.username || tokenUserInfo.nickname,
            avatar: tokenUserInfo.avatar,
            wechatOpenId: tokenUserInfo.wechatOpenId,
          }
          setUser(user)
        }

        // 直接设置为已认证状态
        console.log('🎯 设置认证状态为 AUTHENTICATED')
        setAuthStatus(AuthStatus.AUTHENTICATED)
        setError(null)

        // 检查是否需要刷新token
        if (shouldRefreshToken()) {
          console.log('⏰ Token即将过期，需要刷新')
          // TODO: 实现token刷新逻辑
        }
      } else {
        console.log('❌ 本地没有有效token，尝试微信授权流程')

        // 没有有效token，尝试微信授权流程
        const authResult = await performAuthFlow()

        if (authResult.success && authResult.data) {
          console.log('✅ 微信授权成功')
          setAuthenticatedUser(authResult.data)
        } else if (authResult.needPhoneBinding) {
          console.log('📱 需要绑定手机号')
          setAuthStatus(AuthStatus.NEED_PHONE_BINDING)
          setWxCode(authResult.wxCode || null)
          setError(authResult.error || null)
        } else {
          console.log('❌ 微信授权失败')
          setAuthStatus(AuthStatus.UNAUTHENTICATED)
          setError(authResult.error || null)
        }
      }
    } catch (error: any) {
      console.error('检查认证状态失败:', error)
      setAuthStatus(AuthStatus.UNAUTHENTICATED)
      setError(error.message || '认证检查失败')
      clearLocalToken()
    } finally {
      setIsLoading(false)
      console.log('🏁 认证检查完成')
    }
  }, [setAuthenticatedUser, authStatus])

  // 手机号登录
  const login = useCallback(
    async (mobile: string, smsCode: string): Promise<boolean> => {
      setIsLoading(true)
      setError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          setError('手机号格式不正确')
          return false
        }

        const encryptedMobile = aesEncrypt(mobile)
        const loginResponse = await loginBySMSCode(encryptedMobile, smsCode)
        setAuthenticatedUser(loginResponse)
        return true
      } catch (error: any) {
        console.error('登录失败:', error)
        setError(error.response?.data?.title || '登录失败')
        return false
      } finally {
        setIsLoading(false)
      }
    },
    [setAuthenticatedUser]
  )

  // 手机号注册（有微信code的情况）
  const signup = useCallback(
    async (mobile: string, smsCode: string): Promise<boolean> => {
      // 在非微信环境下，直接使用手机号登录API
      if (!isFromWeiXin() || !wxCode) {
        return login(mobile, smsCode)
      }

      setIsLoading(true)
      setError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          setError('手机号格式不正确')
          return false
        }

        const loginResponse = await signupBySMSCode(mobile, smsCode, wxCode)
        setAuthenticatedUser(loginResponse)
        return true
      } catch (error: any) {
        console.error('注册失败:', error)
        setError(error.response?.data?.title || '注册失败')
        return false
      } finally {
        setIsLoading(false)
      }
    },
    [wxCode, setAuthenticatedUser, login]
  )

  // 绑定手机号
  const bindPhone = useCallback(
    async (mobile: string, smsCode: string): Promise<boolean> => {
      // 在非微信环境下，直接使用手机号登录API
      if (!isFromWeiXin() || !wxCode) {
        return login(mobile, smsCode)
      }

      setIsLoading(true)
      setError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          setError('手机号格式不正确')
          return false
        }

        const loginResponse = await bindPhoneWechat(mobile, wxCode, smsCode)
        setAuthenticatedUser(loginResponse)
        return true
      } catch (error: any) {
        console.error('绑定手机号失败:', error)
        setError(error.response?.data?.title || '绑定失败')
        return false
      } finally {
        setIsLoading(false)
      }
    },
    [wxCode, setAuthenticatedUser, login]
  )

  // 发送短信验证码
  const sendSMSCode = useCallback(
    async (mobile: string, type: SMSType = SMSType.LOGIN): Promise<boolean> => {
      setError(null)

      try {
        if (!validatePhoneNumber(mobile)) {
          setError('手机号格式不正确')
          return false
        }

        await getSMSCode(mobile, type)
        return true
      } catch (error: any) {
        console.error('发送验证码失败:', error)
        setError(error.response?.data?.title || '发送验证码失败')
        return false
      }
    },
    []
  )

  // 退出登录
  const logout = useCallback(async (): Promise<void> => {
    try {
      await apiLogout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地状态
      clearLocalToken()
      setUser(null)
      setAuthStatus(AuthStatus.UNAUTHENTICATED)
      setWxCode(null)
      setError(null)
    }
  }, [])

  // 监听认证相关事件
  useEffect(() => {
    const handleTokenExpired = () => {
      console.log('🔄 Token已过期，清除认证状态')
      setUser(null)
      setAuthStatus(AuthStatus.UNAUTHENTICATED)
      setError('登录已过期，请重新登录')
    }

    const handleNeedPhoneBinding = () => {
      console.log('🔄 需要绑定手机号')
      setAuthStatus(AuthStatus.NEED_PHONE_BINDING)
      setError('需要绑定手机号')
    }

    const handleAuthRequired = () => {
      console.log('🔄 需要重新认证')
      setUser(null)
      setAuthStatus(AuthStatus.UNAUTHENTICATED)
      setError('需要重新登录')
    }

    // 监听HTTP拦截器触发的认证事件
    window.addEventListener('auth:tokenExpired', handleTokenExpired)
    window.addEventListener('auth:needPhoneBinding', handleNeedPhoneBinding)
    window.addEventListener('auth:authRequired', handleAuthRequired)

    return () => {
      window.removeEventListener('auth:tokenExpired', handleTokenExpired)
      window.removeEventListener('auth:needPhoneBinding', handleNeedPhoneBinding)
      window.removeEventListener('auth:authRequired', handleAuthRequired)
    }
  }, [])

  // 初始化时检查认证状态
  useEffect(() => {
    // 只在组件挂载时检查一次
    checkAuthStatus()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    authStatus,
    user,
    isLoading,
    error,
    wxCode,
    login,
    signup,
    bindPhone,
    sendSMSCode,
    logout,
    clearError,
    checkAuthStatus,
  }
}
